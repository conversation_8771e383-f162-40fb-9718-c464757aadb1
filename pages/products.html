<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - BDC</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/dark-mode.css">
    <link rel="stylesheet" href="../css/navbar-fix.css">
    <link rel="stylesheet" href="../css/products-layout-fix.css">
    <script src="../js/translations.js"></script>
</head>
<body>
    <!-- Navigation Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    <img src="../images/bdc_logo.png" alt="BDC Logo" style="height: 40px;">
                </a>
                
                <nav class="nav-menu">
                    <ul>
                        <li><a href="../index.html" data-translate="nav.home">Home</a></li>
                        <li><a href="products.html" class="active" data-translate="nav.products">Products</a></li>
                        <li><a href="about.html" data-translate="nav.about">About Us</a></li>
                        <li><a href="contact.html" data-translate="nav.contact">Contact</a></li>
                    </ul>
                </nav>

                <div class="header-controls">
                    <!-- Language Switcher -->
                    <div class="language-switcher">
                        <button class="lang-btn" onclick="toggleLanguage()">
                            <i class="fas fa-globe"></i>
                            <span id="current-lang">EN</span>
                        </button>
                    </div>

                    <!-- Dark Mode Toggle -->
                    <div class="theme-toggle">
                        <button class="theme-btn" onclick="toggleTheme()">
                            <i class="fas fa-moon" id="theme-icon"></i>
                        </button>
                    </div>

                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Products Hero -->
    <section class="products-hero">
        <div class="container">
            <h1 data-translate="products.hero.title">Our Premium Products</h1>
            <p data-translate="products.hero.subtitle">Discover our complete range of professional cleaning solutions</p>
        </div>
    </section>

    <!-- Products Container -->
    <div class="container">
        <div class="products-container">
            <!-- Sidebar -->
            <aside class="sidebar">
                <h3 data-translate="products.categories.title">Categories</h3>
                <ul class="sidebar-categories">
                    <li>
                        <a href="#" class="category-item active" data-category="all-products">
                            <i class="fas fa-th-large"></i>All Products
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="detergent-powder">
                            <i class="fas fa-box"></i>Detergent Powder
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="dishwashing">
                            <i class="fas fa-utensils"></i>Dishwashing Liquid
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="floor-cleaner">
                            <i class="fas fa-home"></i>Floor Cleaner
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="detergent-liquid">
                            <i class="fas fa-tint"></i>Detergent Liquid
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="glass-cleaner">
                            <i class="fas fa-window-maximize"></i>Glass Cleaner
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="hand-washing">
                            <i class="fas fa-hands-wash"></i>Hand Washing
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="bleach">
                            <i class="fas fa-flask"></i>Bleach
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="toilet-cleaner">
                            <i class="fas fa-toilet"></i>Toilet Cleaner
                        </a>
                    </li>
                </ul>
            </aside>

            <!-- Main Products Area -->
            <main class="products-main">
                <div class="products-header">
                    <div class="products-count">
                        <span id="product-count">14</span> <span>products found</span>
                    </div>
                    <div class="view-info">
                        <i class="fas fa-list"></i> List View
                    </div>
                </div>

                <!-- Dynamic Product Display -->
                <div id="products-display">
                    <!-- All Products Section -->
                    <section id="all-products" class="product-section active">
                        <h2 class="section-title">All Products</h2>
                        <div class="products-grid" id="all-products-grid">
                            <!-- Products will be loaded dynamically -->
                        </div>
                    </section>

                    <!-- Category Sections -->
                    <section id="detergent-powder" class="product-section">
                        <h2 class="section-title">Detergent Powder</h2>
                        <div class="products-grid" id="detergent-powder-grid"></div>
                    </section>

                    <section id="dishwashing" class="product-section">
                        <h2 class="section-title">Dishwashing Liquid</h2>
                        <div class="products-grid" id="dishwashing-grid"></div>
                    </section>

                    <section id="floor-cleaner" class="product-section">
                        <h2 class="section-title">Floor Cleaner</h2>
                        <div class="products-grid" id="floor-cleaner-grid"></div>
                    </section>

                    <section id="detergent-liquid" class="product-section">
                        <h2 class="section-title">Detergent Liquid</h2>
                        <div class="products-grid" id="detergent-liquid-grid"></div>
                    </section>

                    <section id="glass-cleaner" class="product-section">
                        <h2 class="section-title">Glass Cleaner</h2>
                        <div class="products-grid" id="glass-cleaner-grid"></div>
                    </section>

                    <section id="hand-washing" class="product-section">
                        <h2 class="section-title">Hand Washing</h2>
                        <div class="products-grid" id="hand-washing-grid"></div>
                    </section>

                    <section id="bleach" class="product-section">
                        <h2 class="section-title">Bleach</h2>
                        <div class="products-grid" id="bleach-grid"></div>
                    </section>

                    <section id="toilet-cleaner" class="product-section">
                        <h2 class="section-title">Toilet Cleaner</h2>
                        <div class="products-grid" id="toilet-cleaner-grid"></div>
                    </section>
                </div>
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>About BDC</h3>
                    <p>Leading provider of premium cleaning solutions for homes and businesses worldwide. Trusted by millions for over 20 years.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Product Categories</h3>
                    <ul>
                        <li><a href="#detergent-powder">Detergent Powder</a></li>
                        <li><a href="#dishwashing">Dishwashing Liquid</a></li>
                        <li><a href="#floor-cleaner">Floor Cleaner</a></li>
                        <li><a href="#glass-cleaner">Glass Cleaner</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> +****************</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> 123 Clean Street, City, State 12345</p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 BDC. All rights reserved. Professional cleaning solutions for every need.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../js/translations.js"></script>
    <script src="../js/theme.js"></script>
    <script src="../js/main.js"></script>

    <script>
        // Enhanced Product Data with sizes and pricing
        const productData = {
            'detergent-powder': [
                {
                    id: 'summer-flower',
                    title: 'Detergent Powder (Summer Flower)',
                    icon: 'fas fa-seedling',
                    mainImage: '../images/products/lix_detergent_summer_flower.png',
                    sizes: [
                        { size: '150g', price: '$0.25', image: '../images/products/lix_detergent_summer_flower.png' },
                        { size: '300g', price: '$0.45', image: '../images/products/lix_detergent_summer_flower.png' },
                        { size: '500g', price: '$0.75', image: '../images/products/lix_detergent_summer_flower.png' },
                        { size: '1kg', price: '$1.375', image: '../images/products/lix_detergent_summer_flower.png' }
                    ],
                    benefits: 'Removes stains and odors on clothes, leaves a floral scent, gentle on hands; suitable for both machine and hand wash.',
                    usage: 'Separate white and colored clothes. Dissolve 30g of powder in 3-4L of water; stir to form foam and soak garments for up to 30 min. Rinse 2-3 times with clean water and sun-dry.'
                },
                {
                    id: 'lemon',
                    title: 'Detergent Powder (Lemon)',
                    icon: 'fas fa-lemon',
                    mainImage: '../images/products/lix_detergent_lemon_.png',
                    sizes: [
                        { size: '150g', price: '$0.25', image: '../images/products/lix_detergent_lemon_.png' },
                        { size: '300g', price: '$0.45', image: '../images/products/lix_detergent_lemon_.png' },
                        { size: '500g', price: '$0.75', image: '../images/products/lix_detergent_lemon_.png' },
                        { size: '1kg', price: '$1.375', image: '../images/products/lix_detergent_lemon_.png' }
                    ],
                    benefits: 'Whitens fabrics, natural lemon scent, gentle on hands; suitable for both machine and hand wash.',
                    usage: 'Separate white and colored clothes. Dissolve 30g of powder in 3-4L of water; stir to form foam and soak garments for up to 30 min. Rinse 2-3 times with clean water and sun-dry.'
                },
                {
                    id: 'pises',
                    title: 'Detergent Powder (PISES)',
                    icon: 'fas fa-bolt',
                    mainImage: '../images/products/lix_detergent_pises_.png',
                    sizes: [
                        { size: '120g', price: '$0.25', image: '../images/products/lix_detergent_pises_.png' },
                        { size: '350g', price: '$0.625', image: '../images/products/lix_detergent_pises_.png' },
                        { size: '800g', price: '$1.375', image: '../images/products/lix_detergent_pises_.png' }
                    ],
                    benefits: 'Dissolves easily, double stain-removal power, restores brightness; suitable for both machine and hand wash.',
                    usage: 'Separate white and colored clothes. Dissolve 30g of powder in 3-4L of water; stir to form foam and soak garments for up to 30 min. Rinse 2-3 times with clean water and sun-dry.'
                }
            ],
            'dishwashing': [
                {
                    id: 'dishwashing-liquid',
                    title: 'Dishwashing Liquid',
                    icon: 'fas fa-utensils',
                    mainImage: '../images/products/lix_dishwashing_15_750ml.png',
                    sizes: [
                        { size: '200mL', price: '$0.30', image: '../images/products/lix_dishwashing_48_200ml.jpg' },
                        { size: '380mL', price: '$0.50', image: '../images/products/lix_dishwashing_24_380ml.png' },
                        { size: '750mL', price: '$0.875', image: '../images/products/lix_dishwashing_15_750ml.png' },
                        { size: '1.5L', price: '$1.75', image: '../images/products/lix_dishwashing_8_1500ml.png' }
                    ],
                    benefits: 'Made from 100% natural lemon, double-strength oil removal, contains vitamin E to protect hands, refreshing lemon scent.',
                    usage: 'Mix 2 TBSP in 4-5L of water; use a damp sponge to create soapy bubbles; wash dishes; rinse thoroughly.'
                }
            ],
            'floor-cleaner': [
                {
                    id: 'antibacterial',
                    title: 'Floor Cleaner (Antibacterial)',
                    icon: 'fas fa-home',
                    mainImage: '../images/products/lix_floor_cleaner_antibacterial_12_1000ml.png',
                    sizes: [
                        { size: '1L', price: '$2.25', image: '../images/products/lix_floor_cleaner_antibacterial_12_1000ml.png' },
                        { size: '2L', price: '$4.25', image: '../images/products/lix_floor_cleaner_antibacterial_8_2000ml.png' },
                        { size: '5L', price: '$9.75', image: '../images/products/lix_floor_cleaner_antibacterial_3_5000ml.png' }
                    ],
                    benefits: 'Kills 99.9% of bacteria and germs, leaves floors sparkling clean, pleasant fresh scent, safe for all floor types.',
                    usage: 'Dilute with water as needed. Apply to floor surface, mop thoroughly, and let air dry. No rinsing required.'
                },
                {
                    id: 'lily-rose',
                    title: 'Floor Cleaner (Lily & Rose)',
                    icon: 'fas fa-spa',
                    mainImage: '../images/products/lix_floor_cleaner_lixly_&_rose_12_1000ml.png',
                    sizes: [
                        { size: '1L', price: '$2.75', image: '../images/products/lix_floor_cleaner_lixly_&_rose_12_1000ml.png' },
                        { size: '2L', price: '$5.25', image: '../images/products/lix_floor_cleaner_lixly_&_rose_8_2000ml.png' },
                        { size: '5L', price: '$12.25', image: '../images/products/lix_floor_cleaner_lixly_&_rose_3_5000ml.png' }
                    ],
                    benefits: 'Relaxing lily & rose scent, antibacterial formula, gentle on surfaces, leaves a protective shine, aromatherapy benefits while cleaning.',
                    usage: 'Dilute with water as needed. Apply to floor surface, mop thoroughly, and let air dry. No rinsing required.'
                }
            ],
            'detergent-liquid': [
                {
                    id: 'detergent-liquid',
                    title: 'Detergent Liquid',
                    icon: 'fas fa-tint',
                    mainImage: '../images/products/lix_detergent_lixquid_8_2000ml.png',
                    sizes: [
                        { size: '2L', price: '$6.25', image: '../images/products/lix_detergent_lixquid_8_2000ml.png' },
                        { size: '4L', price: '$11.75', image: '../images/products/lix_detergent_lixquid_4_4000ml.png' },
                        { size: '4L Perfume', price: '$12.75', image: '../images/products/lix_detergent_lixquid_perfume_4_4000ml.png' }
                    ],
                    benefits: 'Concentrated formula for maximum cleaning power, removes tough stains, gentle on fabrics, suitable for all washing machines.',
                    usage: 'Use 30-50ml per load depending on soil level. Add to washing machine dispenser or directly to drum before adding clothes.'
                },
                {
                    id: 'fresh-sakura',
                    title: 'Detergent Liquid (Fresh Sakura)',
                    icon: 'fas fa-heart',
                    mainImage: '../images/products/lix_detergent_lixquid_fresh_sakura_4_3000ml.jpg',
                    sizes: [
                        { size: '3L', price: '$10.50', image: '../images/products/lix_detergent_lixquid_fresh_sakura_4_3000ml.jpg' }
                    ],
                    benefits: 'Fresh sakura fragrance, gentle formula, effective cleaning power, suitable for all fabric types.',
                    usage: 'Use 30-50ml per load depending on soil level. Add to washing machine dispenser or directly to drum before adding clothes.'
                }
            ],
            'glass-cleaner': [
                {
                    id: 'glass-cleaner',
                    title: 'Glass Cleaner (Crystal Clear)',
                    icon: 'fas fa-window-maximize',
                    mainImage: '../images/products/lix_glass_cleaner_12_650ml.png',
                    sizes: [
                        { size: '650mL', price: '$2.25', image: '../images/products/lix_glass_cleaner_12_650ml.png' },
                        { size: '5L', price: '$15.75', image: '../images/products/lix_glass_cleaner_5l.png' }
                    ],
                    benefits: 'Streak-free formula, crystal clear results, removes fingerprints and smudges, anti-static properties, suitable for all glass surfaces.',
                    usage: 'Spray directly onto glass surface or apply with cloth. Wipe clean with lint-free cloth or paper towel for streak-free results.'
                }
            ],
            'hand-washing': [
                {
                    id: 'hand-washing',
                    title: 'Hand Washing Liquid',
                    icon: 'fas fa-hands-wash',
                    mainImage: '../images/products/lix_hand_washing_bamboo_charcoal_12_500ml.png',
                    sizes: [
                        { size: '500mL Bamboo', price: '$3.25', image: '../images/products/lix_hand_washing_bamboo_charcoal_12_500ml.png' },
                        { size: '500mL Sakura', price: '$3.25', image: '../images/products/lix_hand_washing_sakura_blossom_12_500ml.png' }
                    ],
                    benefits: 'Gentle on hands, moisturizing formula, antibacterial properties, available in bamboo charcoal and sakura blossom scents.',
                    usage: 'Apply small amount to wet hands, lather well, and rinse thoroughly with clean water. Suitable for frequent use.'
                }
            ],
            'bleach': [
                {
                    id: 'ojavel',
                    title: 'Ojavel Bleach',
                    icon: 'fas fa-tint',
                    mainImage: '../images/products/lix_o_javel_24_300ml.png',
                    sizes: [
                        { size: '300mL', price: '$1.25', image: '../images/products/lix_o_javel_24_300ml.png' }
                    ],
                    benefits: 'Powerful whitening action, removes tough stains, disinfects surfaces, suitable for white fabrics and cleaning.',
                    usage: 'Dilute with water as directed. Apply to stained areas, let sit for recommended time, then rinse thoroughly. For white fabrics only.'
                }
            ],
            'toilet-cleaner': [
                {
                    id: 'toilet-cleaner',
                    title: 'Toilet Cleaner',
                    icon: 'fas fa-toilet',
                    mainImage: '../images/products/lix_toilet_cleaner_900ml.png',
                    sizes: [
                        { size: '900mL', price: '$3.75', image: '../images/products/lix_toilet_cleaner_900ml.png' }
                    ],
                    benefits: 'Powerful cleaning action, removes limescale and stains, fresh scent, disinfects and deodorizes.',
                    usage: 'Apply under toilet rim and on surfaces. Let sit for 10-15 minutes, scrub with toilet brush, then flush. Use regularly for best results.'
                }
            ]
        };

        // Create enhanced product card HTML
        function createProductCard(product) {
            return `
                <div class="product-card" data-category="${product.category}">
                    <div class="product-content">
                        <div class="product-left">
                            <div class="main-product-image">
                                <img src="${product.mainImage}" alt="${product.title}" id="main-img-${product.id}" onerror="this.src='../images/placeholder.svg'">
                            </div>

                            <div class="product-header">
                                <div class="product-icon">
                                    <i class="${product.icon}"></i>
                                </div>
                                <div class="product-info">
                                    <h3 class="product-title">${product.title}</h3>

                                    <div class="product-details">
                                        <div class="detail-section">
                                            <h4><i class="fas fa-star"></i> <span data-translate="products.benefits">Benefits</span></h4>
                                            <p class="product-description">${product.benefits}</p>
                                        </div>

                                        <div class="detail-section">
                                            <h4><i class="fas fa-info-circle"></i> <span data-translate="products.how_to_use">How to Use</span></h4>
                                            <p class="product-usage">${product.usage}</p>
                                        </div>

                                        <div class="detail-section caution-section">
                                            <h4><i class="fas fa-exclamation-triangle"></i> <span data-translate="products.caution_storage">Caution & Storage</span></h4>
                                            <div class="caution-content">
                                                <p><strong data-translate="products.caution">Caution:</strong> <span data-translate="products.caution_text">Avoid contact with eyes; keep out of reach of children.</span></p>
                                                <p><strong data-translate="products.storage">Storage:</strong> <span data-translate="products.storage_text">Close lid after use; store in a cool, dry place.</span></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="product-right">
                            <div class="size-price-selector">
                                <h4><i class="fas fa-tag"></i> Size & Price</h4>
                                <div class="size-options">
                                    ${product.sizes.map((size, index) => `
                                        <div class="size-option ${index === 0 ? 'active' : ''}"
                                             onclick="selectSize('${product.id}', '${size.size}', '${size.price}', '${size.image}', this)">
                                            ${size.size}
                                        </div>
                                    `).join('')}
                                </div>
                                <div class="current-price" id="price-${product.id}">
                                    ${product.sizes[0].price}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Select size function
        window.selectSize = function(productId, size, price, imageSrc, element) {
            const mainImg = document.getElementById(`main-img-${productId}`);
            const priceElement = document.getElementById(`price-${productId}`);

            if (mainImg) mainImg.src = imageSrc;
            if (priceElement) priceElement.textContent = price;

            // Update active states
            const productCard = element.closest('.product-card');
            productCard.querySelectorAll('.size-option').forEach(opt => opt.classList.remove('active'));
            element.classList.add('active');
        }

        // Show category function
        function showCategory(category) {
            console.log('showCategory called with:', category);

            // Hide all sections
            document.querySelectorAll('.product-section').forEach(section => {
                section.classList.remove('active');
            });

            // Show selected section
            const targetSection = document.getElementById(category);
            if (targetSection) {
                targetSection.classList.add('active');

                // Load products if not already loaded
                if (category === 'all-products') {
                    loadAllProducts();
                } else {
                    loadProducts(category);
                }
            }

            // Update sidebar active state
            document.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeItem = document.querySelector(`[data-category="${category}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }
        }

        // Load products for specific category
        function loadProducts(category) {
            const grid = document.getElementById(`${category}-grid`);
            if (!grid || !productData[category]) return;

            grid.innerHTML = '';
            productData[category].forEach(product => {
                product.category = category;
                grid.innerHTML += createProductCard(product);
            });

            // Update product count
            updateProductCount(productData[category].length);

            // Apply translations to newly added content
            if (typeof languageManager !== 'undefined') {
                languageManager.applyTranslations();
            }
        }

        // Load all products
        function loadAllProducts() {
            const allGrid = document.getElementById('all-products-grid');
            if (!allGrid) return;

            allGrid.innerHTML = '';
            let totalCount = 0;
            Object.keys(productData).forEach(category => {
                productData[category].forEach(product => {
                    product.category = category;
                    allGrid.innerHTML += createProductCard(product);
                    totalCount++;
                });
            });

            // Update product count
            updateProductCount(totalCount);

            // Apply translations to newly added content
            if (typeof languageManager !== 'undefined') {
                languageManager.applyTranslations();
            }
        }

        // Update product count
        function updateProductCount(count) {
            const countElement = document.getElementById('product-count');
            if (countElement) {
                countElement.textContent = count;
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Enhanced product system initializing...');

            // Load all products by default
            try {
                loadAllProducts();
                console.log('All products loaded successfully');
            } catch (error) {
                console.error('Error loading products:', error);
            }

            // Add click handlers to category items
            const categoryItems = document.querySelectorAll('.category-item');
            categoryItems.forEach((item, index) => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const category = this.getAttribute('data-category');
                    console.log('Category clicked:', category);
                    showCategory(category);
                });
            });
        });
    </script>
</body>
</html>
