<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - BDC</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/dark-mode.css">
    <link rel="stylesheet" href="../css/navbar-fix.css">
    <style>
        /* Clean Products Page Styles */
        .products-hero {
            background: linear-gradient(135deg, var(--primary-red) 0%, var(--dark-red) 100%);
            padding: 4rem 0;
            color: white;
            text-align: center;
        }

        .products-container {
            display: flex;
            gap: 2rem;
            padding: 2rem 0;
            max-width: 1200px;
            margin: 0 auto;
        }

        .sidebar {
            width: 280px;
            background: var(--gray-50);
            border-radius: 12px;
            padding: 1.5rem;
            height: fit-content;
            position: sticky;
            top: 100px;
            border: 1px solid var(--gray-200);
        }

        .sidebar h3 {
            color: var(--text-dark);
            margin-bottom: 1rem;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .sidebar-categories {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .category-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: var(--text-light);
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .category-item:hover,
        .category-item.active {
            background: var(--primary-red);
            color: white;
            transform: translateX(4px);
        }

        .category-item i {
            margin-right: 0.75rem;
            width: 18px;
            font-size: 1rem;
        }

        .products-main {
            flex: 1;
        }

        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding: 0 1rem;
        }

        .products-count {
            color: var(--text-light);
            font-size: 1rem;
        }

        .view-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .view-info i {
            color: var(--primary-red);
        }

        .products-grid {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--gray-200);
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .product-card:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        .product-image {
            width: 80px;
            height: 80px;
            background: var(--gray-50);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--gray-200);
            flex-shrink: 0;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            padding: 0.5rem;
        }

        .product-info {
            flex: 1;
        }

        .product-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0 0 0.5rem 0;
        }

        .product-description {
            font-size: 0.9rem;
            color: var(--text-light);
            margin: 0;
            line-height: 1.4;
        }

        .product-price {
            background: var(--primary-red);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .product-section {
            display: none;
        }

        .product-section.active {
            display: block;
            animation: fadeIn 0.4s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Mobile Responsive */
        @media (max-width: 1024px) {
            .products-container {
                flex-direction: column;
                padding: 1rem;
            }

            .sidebar {
                width: 100%;
                position: static;
                order: 2;
                margin-top: 2rem;
            }

            .products-main {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .product-card {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
                padding: 1rem;
            }

            .product-image {
                width: 60px;
                height: 60px;
            }

            .products-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .products-container {
                padding: 0.5rem;
            }

            .sidebar {
                padding: 1rem;
            }

            .product-card {
                padding: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="../index.html" class="logo">
                    <img src="../images/bdc_logo.png" alt="BDC Logo" style="height: 40px;">
                </a>
                
                <nav class="nav-menu">
                    <ul>
                        <li><a href="../index.html" data-translate="nav.home">Home</a></li>
                        <li><a href="products.html" class="active" data-translate="nav.products">Products</a></li>
                        <li><a href="about.html" data-translate="nav.about">About Us</a></li>
                        <li><a href="contact.html" data-translate="nav.contact">Contact</a></li>
                    </ul>
                </nav>

                <div class="header-controls">
                    <!-- Language Switcher -->
                    <div class="language-switcher">
                        <button class="lang-btn" onclick="toggleLanguage()">
                            <i class="fas fa-globe"></i>
                            <span id="current-lang">EN</span>
                        </button>
                    </div>

                    <!-- Dark Mode Toggle -->
                    <div class="theme-toggle">
                        <button class="theme-btn" onclick="toggleTheme()">
                            <i class="fas fa-moon" id="theme-icon"></i>
                        </button>
                    </div>

                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Products Hero -->
    <section class="products-hero">
        <div class="container">
            <h1 data-translate="products.hero.title">Our Premium Products</h1>
            <p data-translate="products.hero.subtitle">Discover our complete range of professional cleaning solutions</p>
        </div>
    </section>

    <!-- Products Container -->
    <div class="container">
        <div class="products-container">
            <!-- Sidebar -->
            <aside class="sidebar">
                <h3 data-translate="products.categories.title">Categories</h3>
                <ul class="sidebar-categories">
                    <li>
                        <a href="#" class="category-item active" data-category="all-products">
                            <i class="fas fa-th-large"></i>All Products
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="detergent-powder">
                            <i class="fas fa-box"></i>Detergent Powder
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="dishwashing">
                            <i class="fas fa-utensils"></i>Dishwashing Liquid
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="floor-cleaner">
                            <i class="fas fa-home"></i>Floor Cleaner
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="detergent-liquid">
                            <i class="fas fa-tint"></i>Detergent Liquid
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="glass-cleaner">
                            <i class="fas fa-window-maximize"></i>Glass Cleaner
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="hand-washing">
                            <i class="fas fa-hands-wash"></i>Hand Washing
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="bleach">
                            <i class="fas fa-flask"></i>Bleach
                        </a>
                    </li>
                    <li>
                        <a href="#" class="category-item" data-category="toilet-cleaner">
                            <i class="fas fa-toilet"></i>Toilet Cleaner
                        </a>
                    </li>
                </ul>
            </aside>

            <!-- Main Products Area -->
            <main class="products-main">
                <div class="products-header">
                    <div class="products-count">
                        <span id="product-count">14</span> <span>products found</span>
                    </div>
                    <div class="view-info">
                        <i class="fas fa-list"></i> List View
                    </div>
                </div>

                <!-- Dynamic Product Display -->
                <div id="products-display">
                    <!-- All Products Section -->
                    <section id="all-products" class="product-section active">
                        <div class="products-grid" id="all-products-grid">
                            <!-- Products will be loaded dynamically -->
                        </div>
                    </section>

                    <!-- Category Sections -->
                    <section id="detergent-powder" class="product-section">
                        <div class="products-grid" id="detergent-powder-grid"></div>
                    </section>

                    <section id="dishwashing" class="product-section">
                        <div class="products-grid" id="dishwashing-grid"></div>
                    </section>

                    <section id="floor-cleaner" class="product-section">
                        <div class="products-grid" id="floor-cleaner-grid"></div>
                    </section>

                    <section id="detergent-liquid" class="product-section">
                        <div class="products-grid" id="detergent-liquid-grid"></div>
                    </section>

                    <section id="glass-cleaner" class="product-section">
                        <div class="products-grid" id="glass-cleaner-grid"></div>
                    </section>

                    <section id="hand-washing" class="product-section">
                        <div class="products-grid" id="hand-washing-grid"></div>
                    </section>

                    <section id="bleach" class="product-section">
                        <div class="products-grid" id="bleach-grid"></div>
                    </section>

                    <section id="toilet-cleaner" class="product-section">
                        <div class="products-grid" id="toilet-cleaner-grid"></div>
                    </section>
                </div>
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>About BDC</h3>
                    <p>Leading provider of premium cleaning solutions for homes and businesses worldwide. Trusted by millions for over 20 years.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Product Categories</h3>
                    <ul>
                        <li><a href="#detergent-powder">Detergent Powder</a></li>
                        <li><a href="#dishwashing">Dishwashing Liquid</a></li>
                        <li><a href="#floor-cleaner">Floor Cleaner</a></li>
                        <li><a href="#glass-cleaner">Glass Cleaner</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> +****************</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> 123 Clean Street, City, State 12345</p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 BDC. All rights reserved. Professional cleaning solutions for every need.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../js/translations.js"></script>
    <script src="../js/theme.js"></script>
    <script src="../js/main.js"></script>

    <script>
        // Simple Product Data
        const products = [
            { id: 1, name: 'Detergent Powder (Summer Flower)', category: 'detergent-powder', price: '$4.25', image: '../images/products/lix_detergent_powder_summer_flower_12_1000g.png', description: 'Fresh summer flower scent, powerful stain removal' },
            { id: 2, name: 'Detergent Powder (Lemon)', category: 'detergent-powder', price: '$4.25', image: '../images/products/lix_detergent_powder_lemon_12_1000g.png', description: 'Refreshing lemon scent, excellent grease removal' },
            { id: 3, name: 'Detergent Powder (Pises)', category: 'detergent-powder', price: '$4.75', image: '../images/products/lix_detergent_powder_pises_12_1000g.png', description: 'Premium formula, removes tough stains' },
            { id: 4, name: 'Detergent Powder (Pises Sakura)', category: 'detergent-powder', price: '$5.25', image: '../images/products/lix_detergent_powder_pises_sakura_12_1000g.png', description: 'Delicate sakura fragrance, gentle on sensitive skin' },
            { id: 5, name: 'Detergent Powder (Super Clean)', category: 'detergent-powder', price: '$4.75', image: '../images/products/lix_detergent_powder_super_clean_12_1000g.png', description: 'Maximum cleaning power, professional strength' },
            { id: 6, name: 'Dishwashing Liquid', category: 'dishwashing', price: '$2.25', image: '../images/products/lix_dishwashing_lixquid_12_500ml.png', description: 'Cuts through grease effectively, gentle on hands' },
            { id: 7, name: 'Floor Cleaner (Antibacterial)', category: 'floor-cleaner', price: '$2.25', image: '../images/products/lix_floor_cleaner_antibacterial_12_1000ml.png', description: 'Kills 99.9% of bacteria and germs' },
            { id: 8, name: 'Floor Cleaner (Lily & Rose)', category: 'floor-cleaner', price: '$2.75', image: '../images/products/lix_floor_cleaner_lixly_&_rose_12_1000ml.png', description: 'Relaxing floral scent, antibacterial formula' },
            { id: 9, name: 'Detergent Liquid', category: 'detergent-liquid', price: '$6.25', image: '../images/products/lix_detergent_lixquid_8_2000ml.png', description: 'Concentrated formula for maximum cleaning power' },
            { id: 10, name: 'Detergent Liquid (Fresh Sakura)', category: 'detergent-liquid', price: '$7.25', image: '../images/products/lix_detergent_lixquid_fresh_sakura_4_3000ml.jpg', description: 'Hypoallergenic formula, gentle on sensitive skin' },
            { id: 11, name: 'Glass Cleaner (Crystal Clear)', category: 'glass-cleaner', price: '$2.75', image: '../images/products/lix_glass_cleaner_12_650ml.png', description: 'Streak-free formula, crystal clear results' },
            { id: 12, name: 'Hand Washing Liquid', category: 'hand-washing', price: '$3.25', image: '../images/products/lix_hand_washing_bamboo_charcoal_12_500ml.png', description: 'Gentle on hands, moisturizing formula' },
            { id: 13, name: 'Ojavel (Bleach)', category: 'bleach', price: '$0.375', image: '../images/products/lix_o_javel_24_300ml.png', description: 'Removes stains & odors, for white fabrics only' },
            { id: 14, name: 'Toilet Cleaner (Deep Clean)', category: 'toilet-cleaner', price: '$4.75', image: '../images/products/lix_toilet_cleaner_900ml.png', description: 'Powerful disinfectant formula, kills 99.9% of germs' }
        ];

        // Create product card HTML
        function createProductCard(product) {
            return `
                <div class="product-card">
                    <div class="product-image">
                        <img src="${product.image}" alt="${product.name}" onerror="this.src='../images/placeholder.png'">
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">${product.name}</h3>
                        <p class="product-description">${product.description}</p>
                    </div>
                    <div class="product-price">${product.price}</div>
                </div>
            `;
        }

        // Show category
        function showCategory(category) {
            // Hide all sections
            document.querySelectorAll('.product-section').forEach(section => {
                section.classList.remove('active');
            });

            // Show selected section
            const targetSection = document.getElementById(category);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // Update sidebar active state
            document.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeItem = document.querySelector(`[data-category="${category}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }

            // Load products
            loadProducts(category);
        }

        // Load products for category
        function loadProducts(category) {
            let filteredProducts = products;
            let count = products.length;

            if (category !== 'all-products') {
                filteredProducts = products.filter(product => product.category === category);
                count = filteredProducts.length;
            }

            // Update product count
            document.getElementById('product-count').textContent = count;

            // Get target grid
            const grid = document.querySelector(`#${category}-grid, #all-products-grid`);
            if (!grid) return;

            // Clear and populate grid
            grid.innerHTML = '';
            filteredProducts.forEach(product => {
                grid.innerHTML += createProductCard(product);
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Load all products by default
            loadProducts('all-products');

            // Add click handlers to category items
            document.querySelectorAll('.category-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const category = this.getAttribute('data-category');
                    showCategory(category);
                });
            });
        });
    </script>
</body>
</html>
