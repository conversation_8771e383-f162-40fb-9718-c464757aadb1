/* Enhanced Products Page Styles */

/* Products Hero Section */
.products-hero {
    background: linear-gradient(135deg, var(--primary-red) 0%, var(--dark-red) 100%);
    padding: 4rem 0;
    color: white;
    text-align: center;
}

/* Main Container */
.products-container {
    display: flex;
    gap: 2rem;
    padding: 2rem 0;
    max-width: 1200px;
    margin: 0 auto;
    min-height: calc(100vh - 200px);
}

/* Sidebar Styling */
.sidebar {
    width: 280px;
    background: var(--gray-50);
    border-radius: 12px;
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 100px;
    border: 1px solid var(--gray-200);
    flex-shrink: 0;
}

.sidebar h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 600;
}

.sidebar-categories {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-light);
    text-decoration: none;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-item:hover,
.category-item.active {
    background: var(--primary-red);
    color: white;
    transform: translateX(4px);
}

.category-item i {
    margin-right: 0.75rem;
    width: 18px;
    font-size: 1rem;
}

/* Products Main Area */
.products-main {
    flex: 1;
    min-width: 0;
}

.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 0 1rem;
}

.products-count {
    color: var(--text-light);
    font-size: 1rem;
}

.view-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-light);
    font-size: 0.9rem;
}

.view-info i {
    color: var(--primary-red);
}

/* Section Titles */
.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    padding: 0 1rem;
    border-left: 4px solid var(--primary-red);
    padding-left: 1rem;
}

/* Products Grid - List Layout */
.products-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Enhanced Product Cards */
.product-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.product-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-3px);
}

/* Product Content Layout */
.product-content {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.product-left {
    display: flex;
    gap: 1.5rem;
    flex: 1;
    align-items: flex-start;
}

.product-right {
    min-width: 200px;
    flex-shrink: 0;
}

/* Main Product Image - Larger as requested */
.main-product-image {
    width: 120px;
    height: 120px;
    background: var(--gray-50);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--gray-200);
    flex-shrink: 0;
    overflow: hidden;
}

.main-product-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 0.5rem;
}

/* Product Header Info */
.product-header {
    flex: 1;
}

.product-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-red);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.product-icon i {
    color: white;
    font-size: 1.2rem;
}

.product-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.75rem 0;
    line-height: 1.3;
}

.product-description {
    font-size: 0.95rem;
    color: var(--text-light);
    margin: 0;
    line-height: 1.5;
}

/* Product Details Sections */
.product-details {
    margin-top: 1rem;
}

.detail-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: 8px;
    border-left: 4px solid var(--primary-red);
}

.detail-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 0.75rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.detail-section h4 i {
    color: var(--primary-red);
    font-size: 0.9rem;
}

.detail-section p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
    line-height: 1.5;
}

.product-usage {
    font-style: italic;
    color: var(--text-dark);
}

/* Caution Section Styling */
.caution-section {
    background: #fef3f2;
    border-left-color: #f97316;
}

.caution-section h4 {
    color: #dc2626;
}

.caution-section h4 i {
    color: #f97316;
}

.caution-content p {
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
}

.caution-content p:last-child {
    margin-bottom: 0;
}

.caution-content strong {
    color: var(--text-dark);
    font-weight: 600;
}

/* Fix product sections visibility and spacing */
.product-section {
    margin-bottom: 3rem;
    opacity: 1;
    transform: none;
    transition: opacity 0.3s ease, transform 0.3s ease;
    clear: both;
    overflow: hidden;
    display: none; /* Hidden by default for dynamic system */
}

/* Active product section */
.product-section.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* Fade in animation for active sections */
.product-section.active {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ensure footer stays at bottom */
.footer {
    margin-top: auto;
    clear: both;
}

/* Size & Price Selector */
.size-price-selector {
    background: var(--gray-50);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
}

.size-price-selector h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.size-price-selector h4 i {
    color: var(--primary-red);
}

.size-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.size-option {
    background: white;
    border: 2px solid var(--gray-200);
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--text-dark);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.size-option:hover {
    border-color: var(--primary-red);
    background: var(--light-red);
}

.size-option.active {
    background: var(--primary-red);
    border-color: var(--primary-red);
    color: white;
}

.current-price {
    background: var(--primary-red);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    text-align: center;
}

/* Product Section Management */
.product-section {
    display: none;
}

.product-section.active {
    display: block;
    animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .products-container {
        flex-direction: column;
        padding: 1rem;
        gap: 1.5rem;
    }

    .sidebar {
        width: 100%;
        position: static;
        order: 2;
        margin-top: 1rem;
    }

    .products-main {
        order: 1;
    }

    .product-content {
        flex-direction: column;
        gap: 1.5rem;
    }

    .product-left {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .product-right {
        min-width: auto;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .product-card {
        padding: 1.5rem;
    }

    .product-left {
        gap: 1rem;
    }

    .main-product-image {
        width: 100px;
        height: 100px;
    }

    .product-title {
        font-size: 1.1rem;
    }

    .products-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .size-options {
        justify-content: center;
    }

    .detail-section {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .detail-section h4 {
        font-size: 0.9rem;
    }

    .detail-section p {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .products-container {
        padding: 0.5rem;
    }

    .sidebar {
        padding: 1rem;
    }

    .product-card {
        padding: 1rem;
    }

    .main-product-image {
        width: 80px;
        height: 80px;
    }

    .size-price-selector {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .products-container {
        padding: 2rem 0;
    }

    .products-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1.5rem;
    }

    .product-card {
        padding: 1.5rem;
        min-height: 400px;
    }

    .sidebar {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .products-container {
        padding: 1rem 0;
    }

    .products-grid {
        padding: 0.5rem;
        gap: 1rem;
    }

    .product-card {
        padding: 1rem;
        min-height: 350px;
    }
}

/* Ensure page layout integrity */
html, body {
    overflow-x: hidden;
}

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 24px;
    width: 100%;
    box-sizing: border-box;
}

/* Fix any z-index issues */
.products-container {
    position: relative;
    z-index: 1;
}

.sidebar {
    z-index: 2;
}

.footer {
    position: relative;
    z-index: 3;
}

