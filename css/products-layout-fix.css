/* Product Category Layout Fixes */

/* Ensure proper container structure */
.products-container {
    display: flex;
    gap: 2rem;
    padding: 3rem 0;
    background: white;
    min-height: calc(100vh - 200px); /* Ensure minimum height */
}

/* Fix sidebar positioning */
.sidebar {
    width: 300px;
    background: var(--gray-50);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    height: fit-content;
    max-height: calc(100vh - 150px);
    overflow-y: auto;
    position: sticky;
    top: 120px;
    border: 1px solid var(--gray-200);
    flex-shrink: 0;
}

/* Fix main products area */
.products-main {
    flex: 1;
    min-width: 0; /* Prevent flex item from overflowing */
    overflow: hidden;
}

/* Improve products grid layout */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    background: var(--gray-50);
    padding: 2rem;
    border-radius: var(--border-radius-xl);
    margin-bottom: 3rem; /* Add margin to prevent footer overlap */
}

/* Fix product card heights */
.product-card {
    background: white;
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    height: fit-content;
    min-height: 500px; /* Ensure consistent minimum height */
    display: flex;
    flex-direction: column;
}

/* Ensure product content doesn't overflow */
.product-card > * {
    flex-shrink: 0;
}

/* Fix product sections visibility and spacing */
.product-section {
    margin-bottom: 3rem;
    opacity: 1;
    transform: none;
    transition: opacity 0.3s ease, transform 0.3s ease;
    clear: both;
    overflow: hidden;
    display: none; /* Hidden by default for dynamic system */
}

/* Active product section */
.product-section.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* Fade in animation for active sections */
.product-section.active {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ensure footer stays at bottom */
.footer {
    margin-top: auto;
    clear: both;
}

/* Fix category filtering layout */
.category-detergent-powder,
.category-dishwashing,
.category-floor-cleaner,
.category-detergent-liquid,
.category-glass-cleaner,
.category-hand-washing,
.category-bleach,
.category-toilet-cleaner {
    display: block;
    width: 100%;
    margin-bottom: 2rem;
}

/* Hide filtered products properly */
.product-section.hidden {
    display: none !important;
}

/* Responsive fixes */
@media (max-width: 1024px) {
    .products-container {
        flex-direction: column;
        gap: 2rem;
    }

    .sidebar {
        width: 100%;
        position: static;
        order: 2;
        max-height: none;
        height: auto;
    }

    .products-main {
        order: 1;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    .products-container {
        padding: 2rem 0;
    }

    .products-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1.5rem;
    }

    .product-card {
        padding: 1.5rem;
        min-height: 400px;
    }

    .sidebar {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .products-container {
        padding: 1rem 0;
    }

    .products-grid {
        padding: 0.5rem;
        gap: 1rem;
    }

    .product-card {
        padding: 1rem;
        min-height: 350px;
    }
}

/* Ensure page layout integrity */
html, body {
    overflow-x: hidden;
}

.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 24px;
    width: 100%;
    box-sizing: border-box;
}

/* Fix any z-index issues */
.products-container {
    position: relative;
    z-index: 1;
}

.sidebar {
    z-index: 2;
}

.footer {
    position: relative;
    z-index: 3;
}

